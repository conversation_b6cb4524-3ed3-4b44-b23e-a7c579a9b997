<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Topics API Console Filter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button.danger {
            background-color: #dc3545;
        }
        .test-button.danger:hover {
            background-color: #c82333;
        }
        .test-button.success {
            background-color: #28a745;
        }
        .test-button.success:hover {
            background-color: #218838;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .expected {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .not-expected {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Topics API Console Filter Test</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Open your browser's Developer Console (F12)</li>
                <li>Click the test buttons below</li>
                <li>Check the console output</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🚫 Topics API Messages (Should be FILTERED OUT)</h3>
            <p>These messages should NOT appear in the console:</p>
            
            <button class="test-button danger" onclick="testTopicsAPIMessages()">
                Test Topics API Messages
            </button>
            
            <div class="not-expected">
                <strong>❌ Should NOT see:</strong>
                <ul>
                    <li>"Browsing Topics API removed from https://porfolio-pro.onrender.com/ which is main frame"</li>
                    <li>"Topics API not available"</li>
                    <li>"Privacy Sandbox disabled"</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>✅ Normal Messages (Should be VISIBLE)</h3>
            <p>These messages should appear in the console:</p>
            
            <button class="test-button success" onclick="testNormalMessages()">
                Test Normal Messages
            </button>
            
            <div class="expected">
                <strong>✅ Should see:</strong>
                <ul>
                    <li>"✅ Normal console message - this should be visible"</li>
                    <li>"🚀 Backend status message"</li>
                    <li>"ℹ️ Important user information"</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Extension Error Messages (Should be FILTERED OUT)</h3>
            <p>These extension-related messages should NOT appear:</p>
            
            <button class="test-button danger" onclick="testExtensionMessages()">
                Test Extension Messages
            </button>
            
            <div class="not-expected">
                <strong>❌ Should NOT see:</strong>
                <ul>
                    <li>Extension connection errors</li>
                    <li>Chrome extension messages</li>
                    <li>Performance violation warnings</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Test All Filters</h3>
            <button class="test-button" onclick="testAllFilters()">
                Run Complete Filter Test
            </button>
            <p><em>This will test all filter patterns at once. Check console for results.</em></p>
        </div>
    </div>

    <script>
        // Import the extension error handling (this would normally be done in your React app)
        // For this test, we'll simulate the filtering patterns
        
        function testTopicsAPIMessages() {
            console.log('🧪 Testing Topics API message filtering...');
            
            // These should be filtered out
            console.info('Browsing Topics API removed from https://porfolio-pro.onrender.com/ which is main frame');
            console.log('Topics API not available for this origin');
            console.warn('Privacy Sandbox features disabled');
            console.error('Topics API removed due to policy');
            
            console.log('✅ Topics API filter test completed. Check if any Topics API messages appeared above.');
        }

        function testNormalMessages() {
            console.log('🧪 Testing normal message visibility...');
            
            // These should be visible
            console.log('✅ Normal console message - this should be visible');
            console.info('🚀 Backend status message');
            console.warn('⚠️ Important user warning');
            console.error('❌ Legitimate application error');
            
            console.log('✅ Normal message test completed. All messages above should be visible.');
        }

        function testExtensionMessages() {
            console.log('🧪 Testing extension error filtering...');
            
            // These should be filtered out
            console.error('Could not establish connection. Receiving end does not exist.');
            console.warn('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
            console.log('[Violation] setTimeout\' handler took 152ms');
            console.info('chrome-extension://abc123/content.js error');
            
            console.log('✅ Extension error filter test completed. Check if any extension errors appeared above.');
        }

        function testAllFilters() {
            console.log('🎯 Running complete filter test...');
            console.log('==========================================');
            
            testTopicsAPIMessages();
            console.log('------------------------------------------');
            testNormalMessages();
            console.log('------------------------------------------');
            testExtensionMessages();
            
            console.log('==========================================');
            console.log('🎉 Complete filter test finished!');
            console.log('📊 Summary:');
            console.log('✅ Normal messages should be visible');
            console.log('🚫 Topics API messages should be hidden');
            console.log('🚫 Extension errors should be hidden');
        }

        // Show initial message
        console.log('🔍 Topics API Console Filter Test Page Loaded');
        console.log('📋 Open Developer Console and click the test buttons');
    </script>
</body>
</html>
